#!/usr/bin/env python3
"""
Test script for the GW2APIManager to verify reconnection functionality
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import from bot.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from bot import GW2APIManager

async def test_api_manager():
    """Test the GW2APIManager functionality"""
    print("🧪 Testing GW2APIManager...")
    print("=" * 50)
    
    # Initialize the manager
    manager = GW2APIManager()
    await manager.initialize()
    
    print("✅ Manager initialized")
    
    # Test 1: Health check
    print("\n🔍 Test 1: Health Check")
    is_healthy = await manager.health_check()
    print(f"Health check result: {'✅ Healthy' if is_healthy else '❌ Unhealthy'}")
    
    # Test 2: Get status
    print("\n📊 Test 2: Status Information")
    status = manager.get_status()
    print(f"Status: {status['status_message']}")
    print(f"Success rate: {status['success_rate']:.1f}%")
    print(f"Circuit open: {status['circuit_open']}")
    
    # Test 3: Make a real API request
    print("\n🌐 Test 3: API Request")
    try:
        result = await manager.make_request("build")
        if result:
            print(f"✅ API request successful: Build ID {result.get('id', 'Unknown')}")
        else:
            print("❌ API request failed")
    except Exception as e:
        print(f"❌ API request error: {e}")
    
    # Test 4: Test with invalid endpoint (should fail gracefully)
    print("\n🚫 Test 4: Invalid Endpoint")
    try:
        result = await manager.make_request("invalid_endpoint_test")
        if result:
            print("⚠️ Unexpected success with invalid endpoint")
        else:
            print("✅ Invalid endpoint handled gracefully")
    except Exception as e:
        print(f"✅ Invalid endpoint error handled: {e}")
    
    # Test 5: Final status
    print("\n📈 Test 5: Final Status")
    final_status = manager.get_status()
    print(f"Final status: {final_status['status_message']}")
    print(f"Total requests: {final_status['stats']['total']}")
    print(f"Successful: {final_status['stats']['success']}")
    print(f"Failed: {final_status['stats']['failure']}")
    print(f"Success rate: {final_status['success_rate']:.1f}%")
    
    # Clean up
    await manager.close()
    print("\n🧹 Manager closed")
    print("=" * 50)
    print("✅ All tests completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_api_manager())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
